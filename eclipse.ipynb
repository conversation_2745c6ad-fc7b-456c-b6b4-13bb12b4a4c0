{"cells": [{"cell_type": "code", "execution_count": 1, "id": "12e86ace-2577-4713-aa40-a0c5a34cad7d", "metadata": {"tags": [], "vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mVersionData = swi(9,1,1,[])"]}, "metadata": {}, "output_type": "display_data"}], "source": ["current_prolog_flag(version_data, VersionData)."]}, {"cell_type": "code", "execution_count": 2, "id": "055fe1e8-31c2-4e71-8b41-cac8dda6a721", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["eclipse."]}, {"cell_type": "code", "execution_count": 3, "id": "1329d812-4527-4dce-bad5-d1a8454da09d", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mVersionData = eclipse(7,0,57)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["current_prolog_flag(version_data, VersionData)."]}, {"cell_type": "code", "execution_count": 4, "id": "75dc9dfb-85d1-41a5-8613-335ce5b2c6f3", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mYes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["true."]}, {"cell_type": "code", "execution_count": 5, "id": "5cdaaa14-4521-45af-b0dc-17d91215899b", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["foo"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mYes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["write(foo)."]}, {"cell_type": "code", "execution_count": 6, "id": "eccf0c85-7a85-478a-8d11-7d3600947a0d", "metadata": {"tags": [], "vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["% [ /Users/<USER>/logtalk/examples/aliases/aliases.lgt loaded ]\n", "% [ /Users/<USER>/logtalk/examples/aliases/loader.lgt loaded ]\n", "% (0 warnings)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mYes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["{aliases(loader)}."]}, {"cell_type": "code", "execution_count": 7, "id": "a5b096f7-77d7-464f-927c-ddf875ec53b2", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 7.0.1 (20221109.1506)\n", " -->\n", "<!-- Pages: 1 -->\n", "<svg width=\"174pt\" height=\"218pt\"\n", " viewBox=\"0.00 0.00 173.74 218.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 214)\">\n", "<polygon fill=\"white\" stroke=\"none\" points=\"-4,4 -4,-214 169.74,-214 169.74,4 -4,4\"/>\n", "<!-- 2 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>2</title>\n", "<ellipse fill=\"none\" stroke=\"black\" cx=\"66.74\" cy=\"-18\" rx=\"27\" ry=\"18\"/>\n", "<text text-anchor=\"middle\" x=\"66.74\" y=\"-14.3\" font-family=\"Times,serif\" font-size=\"14.00\">2</text>\n", "</g>\n", "<!-- 3 -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>3</title>\n", "<ellipse fill=\"none\" stroke=\"black\" cx=\"138.74\" cy=\"-18\" rx=\"27\" ry=\"18\"/>\n", "<text text-anchor=\"middle\" x=\"138.74\" y=\"-14.3\" font-family=\"Times,serif\" font-size=\"14.00\">3</text>\n", "</g>\n", "<!-- a -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>a</title>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"31.43,-87.05 33.2,-87.15 34.96,-87.3 36.69,-87.49 38.39,-87.74 40.05,-88.03 41.67,-88.36 43.23,-88.75 44.73,-89.18 46.17,-89.65 47.54,-90.16 48.84,-90.71 50.06,-91.31 51.2,-91.94 52.25,-92.61 53.22,-93.31 54.09,-94.04 54.87,-94.8 55.56,-95.59 56.15,-96.41 56.65,-97.25 57.05,-98.11 57.36,-98.99 57.58,-99.89 57.7,-100.8 57.73,-101.72 57.68,-102.65 57.54,-103.59 57.31,-104.53 57.01,-105.47 56.63,-106.41 56.18,-107.35 55.66,-108.28 55.08,-109.2 54.43,-110.11 53.72,-111.01 52.97,-111.89 52.16,-112.75 51.3,-113.59 50.41,-114.41 49.48,-115.2 48.51,-115.96 47.51,-116.69 46.48,-117.39 45.43,-118.06 44.35,-118.69 43.26,-119.29 42.15,-119.84 41.02,-120.35 39.89,-120.82 38.74,-121.25 37.58,-121.64 36.42,-121.97 35.24,-122.26 34.07,-122.51 32.89,-122.7 31.71,-122.85 30.52,-122.95 29.34,-123 28.15,-123 26.96,-122.95 25.78,-122.85 24.6,-122.7 23.42,-122.51 22.24,-122.26 21.07,-121.97 19.91,-121.64 18.75,-121.25 17.6,-120.82 16.46,-120.35 15.34,-119.84 14.23,-119.29 13.13,-118.69 12.06,-118.06 11.01,-117.39 9.98,-116.69 8.98,-115.96 8.01,-115.2 7.08,-114.41 6.18,-113.59 5.33,-112.75 4.52,-111.89 3.76,-111.01 3.06,-110.11 2.41,-109.2 1.83,-108.28 1.3,-107.35 0.85,-106.41 0.47,-105.47 0.17,-104.53 -0.05,-103.59 -0.19,-102.65 -0.25,-101.72 -0.21,-100.8 -0.09,-99.89 0.13,-98.99 0.43,-98.11 0.84,-97.25 1.34,-96.41 1.93,-95.59 2.62,-94.8 3.4,-94.04 4.27,-93.31 5.24,-92.61 6.29,-91.94 7.43,-91.31 8.65,-90.71 9.94,-90.16 11.31,-89.65 12.75,-89.18 14.26,-88.75 15.82,-88.36 17.44,-88.03 19.1,-87.74 20.79,-87.49 22.53,-87.3 24.28,-87.15 26.06,-87.05 27.85,-87 29.64,-87 31.43,-87.05\"/>\n", "<text text-anchor=\"middle\" x=\"28.74\" y=\"-101.3\" font-family=\"Times,serif\" font-size=\"14.00\">a</text>\n", "</g>\n", "<!-- a(a,b(2,3)) -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>a(a,b(2,3))</title>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.74,-210 16.74,-210 16.74,-174 70.74,-174 70.74,-210\"/>\n", "<text text-anchor=\"middle\" x=\"43.74\" y=\"-188.3\" font-family=\"Times,serif\" font-size=\"14.00\">a</text>\n", "</g>\n", "<!-- a(a,b(2,3))&#45;&gt;a -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>a(a,b(2,3))&#45;&gt;a</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M40.71,-173.8C38.72,-162.51 36.06,-147.47 33.76,-134.43\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"37.22,-133.91 32.04,-124.67 30.33,-135.12 37.22,-133.91\"/>\n", "<text text-anchor=\"middle\" x=\"41.24\" y=\"-144.8\" font-family=\"Times,serif\" font-size=\"14.00\">1</text>\n", "</g>\n", "<!-- b(2,3) -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>b(2,3)</title>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"129.74,-123 75.74,-123 75.74,-87 129.74,-87 129.74,-123\"/>\n", "<text text-anchor=\"middle\" x=\"102.74\" y=\"-101.3\" font-family=\"Times,serif\" font-size=\"14.00\">b</text>\n", "</g>\n", "<!-- a(a,b(2,3))&#45;&gt;b(2,3) -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>a(a,b(2,3))&#45;&gt;b(2,3)</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M55.68,-173.8C63.87,-162.01 74.88,-146.14 84.2,-132.72\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"86.93,-134.92 89.76,-124.71 81.18,-130.93 86.93,-134.92\"/>\n", "<text text-anchor=\"middle\" x=\"80.24\" y=\"-144.8\" font-family=\"Times,serif\" font-size=\"14.00\">2</text>\n", "</g>\n", "<!-- b(2,3)&#45;&gt;2 -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>b(2,3)&#45;&gt;2</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M95.46,-86.8C90.51,-75.13 83.88,-59.45 78.23,-46.11\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"81.49,-44.83 74.36,-36.99 75.04,-47.56 81.49,-44.83\"/>\n", "<text text-anchor=\"middle\" x=\"90.24\" y=\"-57.8\" font-family=\"Times,serif\" font-size=\"14.00\">1</text>\n", "</g>\n", "<!-- b(2,3)&#45;&gt;3 -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>b(2,3)&#45;&gt;3</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M110.03,-86.8C114.97,-75.13 121.61,-59.45 127.26,-46.11\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"130.45,-47.56 131.12,-36.99 124,-44.83 130.45,-47.56\"/>\n", "<text text-anchor=\"middle\" x=\"126.24\" y=\"-57.8\" font-family=\"Times,serif\" font-size=\"14.00\">2</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["digraph {\n", "\"2\" [shape=\"oval\", label=\"2\"]\n", "\"3\" [shape=\"oval\", label=\"3\"]\n", "\"a\" [shape=\"egg\", label=\"a\"]\n", "\"a(a,b(2,3))\" [shape=\"rect\", label=\"a\"]\n", "\"b(2,3)\" [shape=\"rect\", label=\"b\"]\n", "    \"a(a,b(2,3))\" -> \"a\" [label=\"1\", color=\"black\", style=\"solid\"]\n", "    \"a(a,b(2,3))\" -> \"b(2,3)\" [label=\"2\", color=\"black\", style=\"solid\"]\n", "    \"b(2,3)\" -> \"2\" [label=\"1\", color=\"black\", style=\"solid\"]\n", "    \"b(2,3)\" -> \"3\" [label=\"2\", color=\"black\", style=\"solid\"]\n", "}"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mYes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["show_term(a(a, b(2,3)))."]}, {"cell_type": "code", "execution_count": 8, "id": "72c56f51", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["% [ /Users/<USER>/Documents/Logtalk/logtalk-jupyter-kernel/user.lgt loaded ]\n", "% (0 warnings)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mYes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%user\n", ":- object(ack).\n", "\n", "\t:- info([\n", "\t\tversion is 1:0:0,\n", "\t\tauthor is '<PERSON>',\n", "\t\tdate is 2008-3-31,\n", "\t\tcomment is '<PERSON><PERSON>mann function (general recursive function).'\n", "\t]).\n", "\n", "\t:- public(ack/3).\n", "\t:- mode(ack(+integer, +integer, -integer), one).\n", "\t:- info(ack/3, [\n", "\t\tcomment is 'Ackermann function.',\n", "\t\targnames is ['M', 'N', 'V']\n", "\t]).\n", "\n", "\tack(0, N, V) :-\n", "\t\t!,\n", "\t\tV is N + 1.\n", "\tack(M, 0, V) :-\n", "\t\t!,\n", "\t\tM2 is M - 1,\n", "\t\tack(M2, 1, V).\n", "\tack(M, N, V) :-\n", "\t\tM2 is M - 1,\n", "\t\tN2 is N - 1,\n", "\t\tack(M, N2, V2),\n", "\t\tack(M2, V2, V).\n", "\n", ":- end_object.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "6bb4f434", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["% [ /Users/<USER>/Documents/Logtalk/logtalk-jupyter-kernel/foo.lgt loaded ]\n", "% (0 warnings)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mYes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%file foo.lgt\n", ":- object(foo).\n", "\n", "\t:- public(bar/0).\n", "\tbar.\n", "\n", ":- end_object.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "e6650452", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mYes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["foo::bar."]}, {"cell_type": "code", "execution_count": null, "id": "ab82c7dc", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk_kernel"}, "language_info": {"codemirror_mode": "logtalk", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "Logtalk"}}, "nbformat": 4, "nbformat_minor": 5}