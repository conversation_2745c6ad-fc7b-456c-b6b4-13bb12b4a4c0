{"cells": [{"cell_type": "code", "execution_count": 6, "id": "0d04ad31-4217-4e60-9a7e-e4553914b1c0", "metadata": {}, "outputs": [], "source": ["import xvm"]}, {"cell_type": "code", "execution_count": 7, "id": "80392b45-9c8c-44e8-aad3-d1b10a0293cd", "metadata": {}, "outputs": [{"data": {"text/plain": ["((), True)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["xvm.call('load_logtalk')"]}, {"cell_type": "code", "execution_count": 8, "id": "af54b599-8a0f-431a-807e-ac9ddd83262a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The jupyter_ai extension is already loaded. To reload it, use:\n", "  %reload_ext jupyter_ai\n"]}], "source": ["%load_ext jupyter_ai"]}, {"cell_type": "code", "execution_count": 9, "id": "64d2f3a8-1f5f-4a4d-a1fd-e6be036933d2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/site-packages/langchain_community/llms/openai.py:1057: UserWarning: You are trying to use a chat model. This way of initializing it is no longer supported. Instead, please use: `from langchain_community.chat_models import ChatOpenAI`\n", "  warnings.warn(\n"]}, {"data": {"text/html": ["AI generated code inserted below &#11015;&#65039;"], "text/plain": ["<IPython.core.display.HTML object>"]}, "execution_count": 9, "metadata": {"text/html": {"jupyter_ai": {"model_id": "gpt-3.5-turbo", "provider_id": "openai-chat"}}}, "output_type": "execute_result"}], "source": ["%%ai chatgpt --format code\n", "A Logtalk protocol declaring a foo/1 public predicate"]}, {"cell_type": "code", "execution_count": null, "id": "a1954799", "metadata": {}, "outputs": [], "source": [":- protocol(foo).\n", "\n", "    foo(X) :-\n", "        % predicate body goes here\n", "    \n", ":- end_protocol."]}, {"cell_type": "code", "execution_count": 10, "id": "79dbf3f2-4ce4-4087-87d8-1836c81665e7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/site-packages/langchain_community/llms/openai.py:1057: UserWarning: You are trying to use a chat model. This way of initializing it is no longer supported. Instead, please use: `from langchain_community.chat_models import ChatOpenAI`\n", "  warnings.warn(\n"]}, {"data": {"text/html": ["AI generated code inserted below &#11015;&#65039;"], "text/plain": ["<IPython.core.display.HTML object>"]}, "execution_count": 10, "metadata": {"text/html": {"jupyter_ai": {"model_id": "gpt-3.5-turbo", "provider_id": "openai-chat"}}}, "output_type": "execute_result"}], "source": ["%%ai chatgpt --format code\n", "Load the Logtalk graph_operators pack"]}, {"cell_type": "code", "execution_count": null, "id": "75e5a542", "metadata": {}, "outputs": [], "source": [":- pack_install(graph_operators)."]}, {"cell_type": "code", "execution_count": null, "id": "a23117ff-e31e-406a-a28d-083216e87f23", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}