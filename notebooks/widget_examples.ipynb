{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Logtalk Widget Examples\n", "\n", "This notebook demonstrates the data input widget capabilities of the Logtalk Jupyter kernel.\n", "\n", "Widgets are rendered in the notebook using HTML/JavaScript. Kernel callbacks are used to update widget state. The kernel uses a localhost web server to handle widget callbacks. This webserver is started automatically by the kernel by using the first available port in the range 8900-8999."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["%versions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Text Input Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_text_input(name_input, 'Enter your name:', '<PERSON>')."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::get_widget_value(name_input, Name)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Password Input Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_password_input(password_input, 'Enter your password:')."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::get_widget_value(password_input, Name)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Number Input Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_number_input(age_input, 'Enter your age:', 0, 120, 1, 25)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::get_widget_value(age_input, Age)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_number_input(x_input, 'Enter x:', 0.0, 10.0, 0.02, 5.0)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::get_widget_value(x_input, X)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### List all Widgets"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter_widget_handling::widgets."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Slider Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_slider(temperature_slider, 'Temperature (°C)', -10, 40, 5, 20)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::get_widget_value(temperature_slider, Temperature)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_slider(pressure_slider, 'Pressure (kPa)', -10.5, 25.5, 0.1, 18.0)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::get_widget_value(pressure_slider, Pressure)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Date Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_date_input(birth_date_input, 'Enter your birth date:', '1990-01-01')."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::get_widget_value(birth_date_input, BirthDate)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Time Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_time_input(meeting_time_input, 'Enter meeting time:', '14:00')."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["\n", "jupyter::get_widget_value(meeting_time_input, MeetingTime)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON>ail Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_email_input(email_input, 'Enter your email:', '<EMAIL>', '.+@.+\\\\..+')."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::get_widget_value(email_input, Email)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### URL Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_url_input(url_input, 'Enter a URL:', 'https://www.example.com', 'https?://.+')."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::get_widget_value(url_input, URL)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### File Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_file_input(file_input, 'Select a file:')."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::get_widget_value(file_input, File)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Color Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_color_input(color_input, 'Choose a color:', '#ff0000')."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::get_widget_value(color_input, Color)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Dropdown Widget\n", "\n", "Create a dropdown selection:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_dropdown(color_select, 'Choose a color:', [red, green, blue, yellow, purple])."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::get_widget_value(color_select, Color)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Checkbox Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_checkbox(newsletter_checkbox, 'Subscribe to newsletter', false)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::get_widget_value(newsletter_checkbox, Color)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON> Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_button(action_button, 'Click Me!')."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::get_widget_value(action_button, Clicked)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Form-Based Input System\n", "\n", "For more complex data collection, use the form-based input system:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Simple Contact Form\n", "\n", "Create a form with multiple field types:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_input_form(contact_form, [\n", "    text_field(name, 'Full Name:', ''),\n", "    email_field(email, 'Email Address:', ''),\n", "    number_field(age, 'Age:', 0),\n", "    select_field(country, 'Country:', [usa, canada, uk, germany, france], usa),\n", "    textarea_field(message, 'Message:', '', 4),\n", "    checkbox_field(newsletter, 'Subscribe to newsletter', false)\n", "], [\n", "    title('Contact Information'),\n", "    submit_label('Submit Form'),\n", "    cancel_label('Cancel')\n", "])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Retrieve the form data after submission:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::get_form_data(contact_form, ContactData)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Processing Widget Data\n", "\n", "You can use widget values in Logtalk predicates:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["% Define a predicate that uses widget values\n", "jupyter::get_widget_value(name_input, Name),\n", "jupyter::get_widget_value(age_input, Age),\n", "jupyter::get_widget_value(color_select, Color),\n", "format('User ~w is ~w years old and likes ~w~n', [Name, Age, Color])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Interactive Data Collection\n", "\n", "Create a more complex example that collects survey data:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["% Create a survey form\n", "jupyter::create_input_form(survey_form, [\n", "    text_field(participant_id, 'Participant ID:', ''),\n", "    select_field(experience, 'Programming Experience:', [beginner, intermediate, advanced], beginner),\n", "    number_field(years_coding, 'Years of Coding:', 0),\n", "    select_field(favorite_language, 'Favorite Language:', [python, java, javascript, prolog, logtalk], python),\n", "    textarea_field(comments, 'Additional Comments:', '', 3)\n", "], [\n", "    title('Programming Survey'),\n", "    submit_label('Submit Survey')\n", "])."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["% Process survey results\n", "jupyter::get_form_data(survey_form, SurveyData)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk_kernel"}, "language_info": {"codemirror_mode": "logtalk", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "Logtalk"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}