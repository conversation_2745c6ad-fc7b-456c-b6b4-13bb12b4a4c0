{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "#plt.style.use('_mpl-gallery')\n", "\n", "# Make data\n", "x = np.random.weibull(5.0, size=50000)\n", "#print(x)\n", "plt.hist(x, bins='auto', color='skyblue', edgecolor='black')\n", "#plt.barh(range(20), x[0])\n", "#plt.barh(range(20), x[1], left=x[0], color='g')\n", "#plt.barh(range(20), x[2], left=x[0]+x[1], color='r')\n", "plt.xlabel('Values')\n", "plt.ylabel('Frequency')\n", "plt.title('Histogram')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from matplotlib import pyplot as plt\n", "import numpy as np\n", "\n", "rho = np.random.uniform(0, 25, 5000)\n", "phi = np.random.uniform(0, 2*np.pi, 5000)\n", "\n", "x = np.sqrt(rho) * np.cos(phi)\n", "y = np.sqrt(rho) * np.sin(phi)\n", "\n", "plt.scatter(x, y, s = 4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "D = np.random.normal((3, 5, 4), (1.25, 1.00, 1.25), (100, 3))\n", "print(D)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# make data:\n", "np.random.seed(10)\n", "D = np.random.normal((3, 5, 4), (1.25, 1.00, 1.25), (100, 3))\n", "\n", "# plot\n", "plt.boxplot(D)\n", "\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "plt.style.use('_mpl-gallery-nogrid')\n", "\n", "# make data: correlated + noise\n", "np.random.seed(1)\n", "x = np.random.randn(5000)\n", "y = 1.2 * x + np.random.randn(5000) / 3\n", "\n", "# plot:\n", "fig, ax = plt.subplots()\n", "\n", "ax.hexbin(x, y, gridsize=20)\n", "\n", "ax.set(xlim=(-2, 2), ylim=(-3, 3))\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "plt.style.use('_mpl-gallery-nogrid')\n", "\n", "# make data: correlated + noise\n", "np.random.seed(1)\n", "x = np.random.randn(5000)\n", "y = 1.2 * x + np.random.randn(5000) / 3\n", "\n", "# plot:\n", "fig, ax = plt.subplots()\n", "\n", "ax.hist2d(x, y, bins=(np.arange(-3, 3, 0.1), np.arange(-3, 3, 0.1)))\n", "\n", "ax.set(xlim=(-2, 2), ylim=(-3, 3))\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "species = ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>')\n", "sex_counts = {\n", "    'Male': np.array([73, 34, 61]),\n", "    'Female': np.array([73, 34, 58]),\n", "}\n", "width = 0.6  # the width of the bars: can also be len(x) sequence\n", "\n", "\n", "#fig, ax = plt.subplots()\n", "bottom = np.zeros(3)\n", "\n", "for sex, sex_count in sex_counts.items():\n", "    plt.bar(species, sex_count, width, label=sex, bottom=bottom)\n", "    bottom += sex_count\n", "\n", "    plt.bar_label(label_type='center')\n", "\n", "plt.title('Number of penguins by sex')\n", "plt.legend()\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "# Data for the stack plot\n", "x = [1, 2, 3, 4, 5]\n", "y1 = [2, 3, 4, 5, 6]\n", "y2 = [1, 2, 2, 3, 4]\n", "y3 = [1, 1, 1, 2, 3]\n", "\n", "# Create a stack plot\n", "plt.stackplot(x, y1, y2, y3, labels=['Dataset 1', 'Dataset 2', 'Dataset 3'])\n", "\n", "# Add labels and title\n", "plt.xlabel('X-axis')\n", "plt.ylabel('Y-axis')\n", "plt.title('Basic Stack Plot')\n", "\n", "# Add a legend\n", "plt.legend(loc='upper left')\n", "\n", "# Show the plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input_value = input(\"Enter your input: \")  # Prompt the user for input\n", "print(\"You entered:\", input_value)  # Display the input value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "font = {'family': 'serif',\n", "        'color':  'darkred',\n", "        'weight': 'normal',\n", "        'size': 16,\n", "        }\n", "\n", "x = np.linspace(0.0, 5.0, 100)\n", "y = np.cos(2*np.pi*x) * np.exp(-x)\n", "\n", "plt.title('Damped exponential decay', fontdict=font)\n", "plt.text(2, 0.65, r'$\\cos(2 \\pi t) \\exp(-t)$', fontdict=font)\n", "plt.xlabel('time (s)', fontdict=font)\n", "plt.ylabel('voltage (mV)', fontdict=font)\n", "plt.plot(x, y, 'k')\n", "\n", "# Tweak spacing to prevent clipping of ylabel\n", "plt.subplots_adjust(left=0.15)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "fig, ax = plt.subplots()\n", "print(ax)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ar = [[[1,4],[10,7]],[[10,1],[15,4],[25,6]]]\n", "x = [[tuple(i) for i in j] for j in ar]\n", "print(x)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["%%data\n", "Data = [type-broken_barh, title-'Resource usage', xranges-[[[1,4],[10,7]],[[10,1],[15,4],[25,6]]], yranges-[[2,2],[6,2]], facecolors-[green,cyan]].\n", "\n", "        elif data_type == \"broken_barh\":\n", "            data_xranges = [[tuple(i) for i in j] for j in show_data_dict[\"xranges\"]]\n", "            show_data_dict.pop(\"xranges\", None)\n", "            data_yranges = [tuple(i) for i in show_data_dict[\"yranges\"]]\n", "            show_data_dict.pop(\"yranges\", None)\n", "            data_facecolors = show_data_dict[\"facecolors\"]\n", "            show_data_dict.pop(\"facecolors\", None)\n", "            for i in enumerate(data_xranges):\n", "                plt.broken_barh(data_xranges[i], data_yranges[i], facecolors=data_facecolors[i])\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}