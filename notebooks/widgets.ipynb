{"cells": [{"cell_type": "code", "execution_count": 1, "id": "049956c7", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/html": ["<div class=\"logtalk-input-group\"><label class=\"logtalk-widget-label\" for=\"name_widget\">name</label><br><input type=\"text\" id=\"name_widget\" class=\"logtalk-widget-input\" value=\"paulo\" onchange=\"\t\tfetch('http://localhost:8998', {\t\t\tmethod: 'POST',\t\t\theaders: {'Content-Type': 'application/json'},\t\t\tbody: JSON.stringify({action: 'text_input', widgetId: 'name_widget', value: String(this.value)})\t\t})\t\t.then(response => response.json())\t\t.then(data => console.log('Response:', data))\" style=\"margin: 5px; padding: 5px; border: 1px solid #ccc; border-radius: 3px;\"/></div>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::create_text_input(name_widget, name, paulo)."]}, {"cell_type": "code", "execution_count": null, "id": "8f53e610", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk_kernel"}, "language_info": {"codemirror_mode": "logtalk", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "Logtalk"}}, "nbformat": 4, "nbformat_minor": 5}