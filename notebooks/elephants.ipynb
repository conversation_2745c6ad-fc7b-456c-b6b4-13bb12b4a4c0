{"cells": [{"cell_type": "markdown", "id": "7cc5a9dd-7484-4883-aa02-f7223ab40289", "metadata": {"tags": []}, "source": ["# Elephants as prototypes"]}, {"cell_type": "markdown", "id": "e440cd63-89f3-452c-895a-fc881e9016db", "metadata": {}, "source": ["This is a simple example illustrating the concept of _prototypes_ using elephants. A similar example is often found in knowledge representation discussions.\n", "\n", "This notebook is currently running using:"]}, {"cell_type": "code", "execution_count": 1, "id": "0a4ebf04", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["Logtalk 3.86.0-b01\n", "SWI-Prolog 9.3.14\n", "Logtalk Jupyter kernel 0.15.0-beta"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%versions"]}, {"cell_type": "markdown", "id": "cfd76824-1938-4058-a1e4-22a271995af7", "metadata": {"tags": []}, "source": ["Prototypes are neither instances or classes but either standalone objects as `cly<PERSON>`, our prototypical but concrete elephant:"]}, {"cell_type": "code", "execution_count": 2, "id": "aba97b16-beb7-4bba-b5f5-feb211c7336f", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%file clyde.lgt\n", "\n", ":- object(clyde).\n", "\n", "\t:- public(color/1).\n", "\tcolor(grey).\n", "\n", "\t:- public(number_of_legs/1).\n", "\tnumber_of_legs(4).\n", "\n", ":- end_object."]}, {"cell_type": "code", "execution_count": 3, "id": "76def0f8-6b1a-41b6-8c53-78e65c3acaad", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mN = 4"]}, "metadata": {}, "output_type": "display_data"}], "source": ["clyde::number_of_legs(N)."]}, {"cell_type": "code", "execution_count": 4, "id": "de036ac7-1c74-46fc-9a81-683d7c01cba5", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mC = grey"]}, "metadata": {}, "output_type": "display_data"}], "source": ["clyde::color(C)."]}, {"cell_type": "markdown", "id": "93bc0c05-f9c3-45e3-bf24-de39eb845210", "metadata": {}, "source": ["Or objects that are derived from other prototypes as `fred`, which is like `cly<PERSON>` except in his color:"]}, {"cell_type": "code", "execution_count": 5, "id": "2e37c398-53c7-470d-9297-e29b91677156", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%file fred.lgt\n", "\n", ":- object(fred,\n", "\textends(clyde)).\n", "\n", "\t% override inherited definition\n", "\tcolor(white).\n", "\n", ":- end_object."]}, {"cell_type": "code", "execution_count": 6, "id": "ff5d9c67-a491-4cdc-8d50-9e80cc1b842a", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mN = 4"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fred::number_of_legs(N)."]}, {"cell_type": "code", "execution_count": 7, "id": "24139653-3f6b-4a09-813f-6cb018f8f2a6", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mC = white"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fred::color(C)."]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk_kernel"}, "language_info": {"codemirror_mode": "logtalk", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "Logtalk"}}, "nbformat": 4, "nbformat_minor": 5}