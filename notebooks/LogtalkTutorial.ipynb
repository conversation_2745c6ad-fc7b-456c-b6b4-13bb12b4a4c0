{"cells": [{"cell_type": "markdown", "id": "1ccf1b85-e8e1-445c-9a1c-307c615c6152", "metadata": {}, "source": ["# Logtalk tutorial\n", "\n", "Logtalk is an *object-oriented logic programming language* that extends and leverages Prolog with modern code encapsulation and code reuse mechanisms without compromising its declarative programming features. Logtalk is implemented in highly portable code and can use most modern and standards compliant Prolog implementations as a backend compiler.\n", "\n", "To keep its size reasonable, this tutorial necessarily assumes that the reader have a working knowledge of Prolog and is biased towards describing Logtalk object-oriented features.\n", "\n", "Running this notebook assumes Logtalk is installed using one of the provided installers or by running the manual installation script.\n", "\n", "The default backend can be changed in the fly by adding a code cell at the top and running one of the following queries: `eclipse`, `gnu`, `sicstus`, `swi`, `trealla`, `xvm`, or `yap` (assuming that all these backend Prolog systems are installed). The default backend can be set for all notebooks in a directory by using a `logtalk_kernel_config.py` file (see the [logtalk-jupyter-kernel](https://github.com/LogtalkDotOrg/logtalk-jupyter-kernel) repo for details). If this file is not present, the default backend is SWI-Prolog.\n", "\n", "This notebook is currently running using:"]}, {"cell_type": "code", "execution_count": 1, "id": "cf11ee56", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["Logtalk 3.90.0-b01\n", "XVM 10.2.2\n", "Logtalk Jupyter kernel 0.27.0-beta"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%versions"]}, {"cell_type": "markdown", "id": "b65065c3", "metadata": {}, "source": ["\n", "## Syntax\n", "\n", "Logtalk uses standard Prolog syntax with the addition of a few operators and directives for a smooth learning curve and wide portability. One important consequence is that Prolog code can be easily encapsulated in objects with little or no changes. Moreover, Logtalk can transparently compile most Prolog modules as Logtalk objects.\n", "\n", "The main operators are:\n", "\n", "* `::/2` - sending a message to an object\n", "* `::/1` - sending a message to _self_ (i.e. to the object that received the message being processed)\n", "* `^^/1` - _super_ call (of an inherited or imported predicate)\n", "\n", "Some of the most important entity and predicate directives will be introduced in the next sections.\n", "\n", "## Entities and roles\n", "\n", "Logtalk provides _objects_, _protocols_, and _categories_ as first-class entities. Relations between entities define _patterns of code reuse_ and the _roles_ played by the entities. For example, when an object _instantiates_ another object, the first object plays the role of an instance and the second object plays the role of a class. An _extends_ relation between two objects implies that both objects play the role of prototypes, with one of them extending the other, its parent prototype.\n", "\n", "## Defining an object\n", "\n", "An object encapsulates predicate _declarations_ and _definitions_. Objects can be created dynamically but are usually static and defined in source files. A single source file can contain any number of entity definitions. A simple object, defining a list member public predicate:"]}, {"cell_type": "code", "execution_count": 2, "id": "ced77e15-7210-44e6-b97b-5ea55a6ed0f1", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%file lists.lgt\n", "\n", ":- object(lists).\n", "\n", "\t:- public(member/2).\n", "\tmember(Head, [Head| _]).\n", "\tmember(Head, [_| Tail]) :-\n", "\t\tmember(<PERSON>, Tail).\n", "\n", ":- end_object."]}, {"cell_type": "markdown", "id": "b18cde73-25f0-4a47-9415-7ce0e03c7c39", "metadata": {}, "source": ["## Compiling and loading source files\n", "\n", "In the notebook cell above, we used the `%%file FILE` *cell magic* to save the cell contents to a `lists.lgt` file and compile/load it when the cell is run. At a Logtalk top-level, assuming that the code above for the `list` object is saved in a `list.lgt` file, it can be compiled and loaded using the `logtalk_load/1` built-in predicate or its abbreviation, `{}/1`, with the file path as argument (the extension can be omitted):\n", "\n", "    ?- {list}.\n", "    yes\n", "\n", "In general, entities may have dependencies on entities defined in other source files (e.g. library entities). To load a file and all its dependencies, the advised solution is to define a _loader_ file that loads all the necessary files for an application. A loader file is simply a source file, typically named `loader.lgt`, that makes calls to the `logtalk_load/1-2` built-in predicates, usually from an `initialization/1` directive for portability and standards compliance. Loader files are provided for all libraries, tools, and examples.\n", "\n", "## Sending a message to an object\n", "\n", "The `::/2` infix operator is used to send a message to an object. As in Prolog, we can backtrack for alternative solutions:"]}, {"cell_type": "code", "execution_count": 3, "id": "3e1eac1b-f1b5-4eea-b206-55434d087338", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mX = 1"]}, "metadata": {}, "output_type": "display_data"}], "source": ["list::member(X, [1,2,3])."]}, {"cell_type": "code", "execution_count": 4, "id": "7818791c-8446-47be-aa75-b73f86580950", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mX = 2"]}, "metadata": {}, "output_type": "display_data"}], "source": ["retry."]}, {"cell_type": "code", "execution_count": 5, "id": "b0a0db8d-c098-4a3c-a5ac-f9ca2255341c", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mX = 3"]}, "metadata": {}, "output_type": "display_data"}], "source": ["retry."]}, {"cell_type": "markdown", "id": "4c9437d6-cd82-49b6-881c-7e4d5a75d559", "metadata": {}, "source": ["Encapsulation is enforced. A predicate can be declared _public_, _protected_, or _private_. It can also be _local_ when there is no scope directive for it. For example:"]}, {"cell_type": "code", "execution_count": 6, "id": "9b2ab55c-da5c-497b-94bf-8990d47b1bf7", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%file scopes.lgt\n", "\n", ":- object(scopes).\n", "\n", "\t:- private(bar/0).\n", "\tbar.\n", "\n", "\tlocal.\n", "\n", ":- end_object."]}, {"cell_type": "markdown", "id": "23f30929", "metadata": {}, "source": ["Sending a message for a declared predicate that is out of scope results in a permission error:"]}, {"cell_type": "code", "execution_count": 7, "id": "a106c9f8-345f-4c13-8f04-21e8fcf7c57f", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mError = error(permission_error(access,private_predicate,bar/0),logtalk(scopes::bar,c(user,user,r(user,scopes,[],[]))))"]}, "metadata": {}, "output_type": "display_data"}], "source": ["catch(scopes::bar, Error, true)."]}, {"cell_type": "markdown", "id": "b8c7cfeb", "metadata": {}, "source": ["Sending a message for a defined but not non-declared predicate results in a existence error:"]}, {"cell_type": "code", "execution_count": 8, "id": "785272e9-2481-47eb-974f-143ed5e50791", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mError = error(existence_error(predicate_declaration,local/0),logtalk(scopes::local,c(user,user,r(user,scopes,[],[]))))"]}, "metadata": {}, "output_type": "display_data"}], "source": ["catch(scopes::local, Error, true)."]}, {"cell_type": "markdown", "id": "90a2dbce-8f35-40d6-be33-584eb5598fbb", "metadata": {}, "source": ["When the predicate in a message is unknown for the object (the role it plays determines the lookup procedures), we also get an error. For example:"]}, {"cell_type": "code", "execution_count": 9, "id": "bf9cf579-64bd-4e25-a0f6-2c3b4107a775", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mError = error(existence_error(predicate_declaration,unknown/0),logtalk(scopes::unknown,c(user,user,r(user,scopes,[],[]))))"]}, "metadata": {}, "output_type": "display_data"}], "source": ["catch(scopes::unknown, Error, true)."]}, {"cell_type": "markdown", "id": "795ab332-53ab-417a-b316-6acdbc44af9c", "metadata": {}, "source": ["A subtle point is that predicate scope directives specify predicate _calling_ semantics, not _definition_ semantics. For example, if an object playing the role of a class declares a predicate private, the predicate can be defined in subclasses and instances *but* can only be called in its instances _from_ the class.\n", "\n", "## Defining and implementing a protocol\n", "\n", "Protocols contain predicate declarations that can be implemented by any number of objects and categories:"]}, {"cell_type": "code", "execution_count": 10, "id": "679eeacb-105b-4c06-918b-0c2e7ed7ca53", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%file lists.lgt\n", "\n", ":- protocol(lists_protocol).\n", "\n", "\t:- public(member/2).\n", "\n", ":- end_protocol.\n", "\n", ":- object(lists,\n", "\timplements(lists_protocol)).\n", "\n", "\tmember(Head, [Head| _]).\n", "\tmember(Head, [_| Tail]) :-\n", "\t\tmember(<PERSON>, Tail).\n", "\n", ":- end_object."]}, {"cell_type": "markdown", "id": "31bb5a4f-e8ed-4fe4-b5f4-90215063edba", "metadata": {}, "source": ["The scope of the protocol predicates can be restricted using protected or private implementation. For example:"]}, {"cell_type": "code", "execution_count": 11, "id": "c3981d1d-da6f-47e8-a03c-d23b88c6b354", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%file stack.lgt\n", "\n", ":- object(stack,\n", "\timplements(private::lists_protocol)).\n", "\n", ":- end_object."]}, {"cell_type": "markdown", "id": "76afbf4f-f945-4ae5-b46d-8d204ed7b0f7", "metadata": {}, "source": ["In fact, all entity relations (in an entity opening directive) can be qualified as public (the default), protected, or private.\n", "\n", "## Prototypes\n", "\n", "An object without an _instantiation_ or _specialization_ relation with another object plays the role of a prototype."]}, {"cell_type": "code", "execution_count": 12, "id": "73e06590-ee0f-436e-b506-5489e76c045c", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%file clyde.lgt\n", "\n", "% clyde, our prototypical elephant\n", ":- object(clyde).\n", "\n", "\t:- public(color/1).\n", "\tcolor(grey).\n", "\n", "\t:- public(number_of_legs/1).\n", "\tnumber_of_legs(4).\n", "\n", ":- end_object."]}, {"cell_type": "markdown", "id": "9ee4d54e", "metadata": {}, "source": [" A prototype can also _extend_ another object, its parent prototype."]}, {"cell_type": "code", "execution_count": 13, "id": "b4af9958-c2f0-4567-9cb4-7c3ff6543ce3", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%file fred.lgt\n", "\n", "% fred, another elephant, is like clyde, except that he's white\n", ":- object(fred,\n", "\textends(clyde)).\n", "\n", "\tcolor(white).\n", "\n", ":- end_object."]}, {"cell_type": "markdown", "id": "f6c9ad73-8fff-4d55-92d6-4bc89072e372", "metadata": {}, "source": ["When answering a message sent to an object playing the role of a prototype, we validate the message and look for an answer first in the prototype itself and, if not found, we delegate to the prototype parents if any:"]}, {"cell_type": "code", "execution_count": 14, "id": "7928fe8b-b935-4d0b-8c75-6aef466d091d", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mN = 4"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fred::number_of_legs(N)."]}, {"cell_type": "code", "execution_count": 15, "id": "0dc1cd62-0851-419e-8e2b-e50ebdd1fb44", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mC = white"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fred::color(C)."]}, {"cell_type": "markdown", "id": "b1abe300-f4ae-4d85-a86a-52055f382538", "metadata": {}, "source": ["A message is valid if the corresponding predicate is _declared_ (and the sender is within scope) but it will fail, rather then throwing an error, if the predicate is not _defined_. This is called the _closed-world assumption_. For example, consider the following object, saved in a `foo.lgt` file:"]}, {"cell_type": "code", "execution_count": 16, "id": "148ff395-7d03-4416-a7cf-5ab0199a29aa", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%file foo.lgt\n", "\n", ":- object(foo).\n", "\n", "\t:- public(bar/0).\n", "\n", ":- end_object."]}, {"cell_type": "markdown", "id": "dba53531-2c93-42e3-ba42-66aa044c5d6f", "metadata": {}, "source": ["Loading the file and trying to call the `bar/0` predicate fails as expected:"]}, {"cell_type": "code", "execution_count": 17, "id": "c6b45c72-04b1-4021-91c0-61674e7c578e", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\\+ foo::bar."]}, {"cell_type": "markdown", "id": "24b0beac", "metadata": {}, "source": ["Note that this is different from calling an _unknown_ predicate, which results in an error:"]}, {"cell_type": "code", "execution_count": 18, "id": "4176fb68-ec74-488f-b95b-303c3ae12bbf", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mError = error(existence_error(predicate_declaration,baz/0),logtalk(foo::baz,c(user,user,r(user,foo,[],[]))))"]}, "metadata": {}, "output_type": "display_data"}], "source": ["catch(foo::baz, <PERSON><PERSON>r, true)."]}, {"cell_type": "markdown", "id": "1660deb4-b32b-4199-89b6-0986df03b950", "metadata": {}, "source": ["## Classes and instances\n", "\n", "In order to define objects playing the role of classes and/or instances, an object must have at least an instantiation or a specialization relation with another object. Objects playing the role of meta-classes can be used when we need to see a class also as an instance. We use the following example to also illustrate how to dynamically create new objects at runtime:"]}, {"cell_type": "code", "execution_count": 19, "id": "950e5328-610c-448c-bb2d-32703f0a0f5e", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%file classes.lgt\n", "\n", "% a simple, generic, metaclass defining a new/2 predicate for its instances\n", ":- object(metaclass,\n", "\tinstantiates(metaclass)).\n", "\n", "\t:- public(new/2).\n", "\tnew(Instance, Clauses) :-\n", "\t\tself(Class),\n", "\t\tcreate_object(Instance, [instantiates(Class)], [], Clauses).\n", "\n", ":- end_object.\n", "\n", "% a simple class defining age/1 and name/1 predicate for its instances\n", ":- object(person,\n", "\tinstantiates(metaclass)).\n", "\n", "\t:- public([\n", "\t\tage/1, name/1\n", "\t]).\n", "\n", "\t% a default value for age/1\n", "\tage(42).\n", "\n", ":- end_object.\n", "\n", "% a static instance of the class person\n", ":- object(john,\n", "\tinstantiates(person)).\n", "\n", "\tname(john).\n", "\tage(12).\n", "\n", ":- end_object."]}, {"cell_type": "markdown", "id": "68e0e111-7bd7-4231-a9f1-0b40dfbebf57", "metadata": {}, "source": ["When answering a message sent to an object playing the role of an instance, we validate the message by starting in its class and going up to its class superclasses if necessary. Assuming that the message is valid, then we look for an answer starting in the instance itself:"]}, {"cell_type": "code", "execution_count": 20, "id": "26d73fc7-ed03-4efc-acc9-bcc658ee11e4", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mInstance = o1"]}, "metadata": {}, "output_type": "display_data"}], "source": ["person::new(Instance, [name(paulo)])."]}, {"cell_type": "code", "execution_count": 21, "id": "30e905e5-e42a-4dfe-825a-bb7fcde37ac1", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mName = paulo"]}, "metadata": {}, "output_type": "display_data"}], "source": ["o1::name(Name)."]}, {"cell_type": "code", "execution_count": 22, "id": "4da8a9df-216c-4247-8a8e-b9eb0147e4b6", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mAge = 42"]}, "metadata": {}, "output_type": "display_data"}], "source": ["o1::age(Age)."]}, {"cell_type": "code", "execution_count": 23, "id": "be4cc5d5-03b6-4a5e-b9bb-7abb79a76447", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mAge = 12"]}, "metadata": {}, "output_type": "display_data"}], "source": ["john::age(Age)."]}, {"cell_type": "markdown", "id": "382d467a-8b50-48bf-a092-98a5467b0d81", "metadata": {}, "source": ["## Categories\n", "\n", "A category is a fine grained unit of code reuse, used to encapsulate a _cohesive_ set of predicate declarations and definitions, implementing a _single_ functionality, that can be imported into any object. A category can thus be seen as the dual concept of a protocol. In the following example, we define categories representing car engines and then import them into car objects:"]}, {"cell_type": "code", "execution_count": 24, "id": "bbe5028b-c2aa-449c-8f4f-7549c03ad2d3", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%file categories.lgt\n", "\n", "% a protocol describing engine characteristics\n", ":- protocol(carenginep).\n", "\n", "\t:- public([\n", "\t\treference/1,\n", "\t\tcapacity/1,\n", "\t\tcylinders/1,\n", "\t\thorsepower_rpm/2,\n", "\t\tbore_stroke/2,\n", "\t\tfuel/1\n", "\t]).\n", "\n", ":- end_protocol.\n", "\n", "% a typical engine defined as a category\n", ":- category(classic,\n", "\timplements(carenginep)).\n", "\n", "\treference('M180.940').\n", "\tcapacity(2195).\n", "\tcylinders(6).\n", "\thorsepower_rpm(94, 4800).\n", "\tbore_stroke(80, 72.8).\n", "\tfuel(gasoline).\n", "\n", ":- end_category.\n", "\n", "% a souped up version of the previous engine\n", ":- category(sport,\n", "\textends(classic)).\n", "\n", "\treference('M180.941').\n", "\thorsepower_rpm(HP, RPM) :-\n", "\t\t^^horsepower_rpm(ClassicHP, ClassicRPM),\t% \"super\" call\n", "\t\tHP is truncate(ClassicHP*1.23),\n", "\t\tRPM is truncate(ClassicRPM*0.762).\n", "\n", ":- end_category.\n", "\n", "% with engines (and other components), we may start \"assembling\" some cars\n", ":- object(sedan,\n", "\timports(classic)).\n", "\n", ":- end_object.\n", "\n", ":- object(coupe,\n", "\timports(sport)).\n", "\n", ":- end_object."]}, {"cell_type": "markdown", "id": "4e2eb99d-d9f9-4c4e-b34e-ef816c2bbf9c", "metadata": {}, "source": ["Categories are independently compiled and thus allow importing objects to be updated by simple updating the imported categories without requiring object recompilation. Categories also provide _runtime transparency_. I.e. the category protocol adds to the protocol of the objects importing the category:"]}, {"cell_type": "code", "execution_count": 25, "id": "cf5623ec-762b-45d0-b0d2-39fc38facdb6", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/markdown": ["Predicate | \n", ":- | \n", "bore_stroke/2 | \n", "capacity/1 | \n", "cylinders/1 | \n", "fuel/1 | \n", "horsepower_rpm/2 | \n", "reference/1 | "], "text/plain": ["Predicate | \n", ":- | \n", "bore_stroke/2 | \n", "capacity/1 | \n", "cylinders/1 | \n", "fuel/1 | \n", "horsepower_rpm/2 | \n", "reference/1 | "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%table\n", "sedan::current_predicate(Predicate)."]}, {"cell_type": "markdown", "id": "1f5ada53", "metadata": {}, "source": ["## Hot patching\n", "\n", "Categories can be also be used for hot-patching objects. A category can add new predicates to an object and/or replace object predicate definitions. For example, consider the following object:"]}, {"cell_type": "code", "execution_count": 26, "id": "ded505fc", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%file buggy.lgt\n", "\n", ":- set_logtalk_flag(complements, allow).\n", "\n", ":- object(buggy).\n", "\n", "\t:- public(p/0).\n", "\tp :- write(foo).\n", "\n", ":- end_object."]}, {"cell_type": "markdown", "id": "a7a3c9ce", "metadata": {}, "source": ["Assume that the object prints the wrong string when sent the message `p/0`:"]}, {"cell_type": "code", "execution_count": 27, "id": "46b07f1e", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["foo"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["buggy::p."]}, {"cell_type": "markdown", "id": "60eb9996", "metadata": {}, "source": ["If the object source code is not available and we need to fix an application running the object code, we can simply define a category that fixes the buggy predicate:"]}, {"cell_type": "code", "execution_count": 28, "id": "80516dff", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%file patch.lgt\n", "\n", ":- category(patch,\n", "\tcomplements(buggy)).\n", "\n", "\t% fixed p/0 def\n", "\tp :- write(bar).\n", "\n", ":- end_category."]}, {"cell_type": "markdown", "id": "f622c161", "metadata": {}, "source": ["We will now get:"]}, {"cell_type": "code", "execution_count": 29, "id": "007e95de", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["bar"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["buggy::p."]}, {"cell_type": "markdown", "id": "e596bbe0", "metadata": {}, "source": ["As hot-patching forcefully breaks encapsulation, the `complements` compiler flag can be set (globally, per source file, or per-object) to allow, restrict, or prevent it.\n", "\n", "## Parametric objects and categories\n", "\n", "Objects and categories can be parameterized by using as identifier a compound term instead of an atom. Object and category parameters are _logical variables_ shared with all encapsulated predicates. An example with geometric circles:"]}, {"cell_type": "code", "execution_count": 30, "id": "40ce66aa", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%file circles.lgt\n", "\n", ":- object(circle(_Radius, _Color)).\n", "\n", "\t:- public([\n", "\t\tarea/1, perimeter/1\n", "\t]).\n", "\n", "\tarea(Area) :-\n", "\t\tparameter(1, <PERSON>dius),\n", "\t\tArea is pi*Radius*Radius.\n", "\n", "\tperimeter(Perimeter) :-\n", "\t\tparameter(1, <PERSON>dius),\n", "\t\tPerimeter is 2*pi*Radius.\n", "\n", ":- end_object."]}, {"cell_type": "markdown", "id": "5adaf6ea", "metadata": {}, "source": ["Parametric objects are used just as any other object, usually providing values for the parameters when sending a message:"]}, {"cell_type": "code", "execution_count": 31, "id": "f82d8e6c", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mArea = 4.7529155256159976"]}, "metadata": {}, "output_type": "display_data"}], "source": ["circle(1.23, blue)::area(Area)."]}, {"cell_type": "markdown", "id": "340f1ec5", "metadata": {}, "source": ["Parametric objects also provide a simple way of associating a set of predicates with a plain Prolog predicate. Prolog facts can be interpreted as _parametric object proxies_ when they have the same functor and arity as the identifiers of parametric objects. Handy syntax is provided to for working with proxies. For example, assuming the following clauses for a `circle/2` predicate:"]}, {"cell_type": "code", "execution_count": 32, "id": "4686ad32", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%user\n", "\n", "circle(1.23, blue).\n", "circle(3.71, yellow).\n", "circle(0.39, green).\n", "circle(5.74, black).\n", "circle(8.32, cyan)."]}, {"cell_type": "markdown", "id": "b36b94cf", "metadata": {}, "source": ["With these clauses loaded, we can easily compute for example a list with the areas of all the circles:"]}, {"cell_type": "code", "execution_count": 33, "id": "163d825f", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mAreas = [4.7529155256159976,43.241195443275274,0.47783624261100754,103.50793811341508,217.4685833038541]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["findall(Area, {circle(_, _)}::area(Area), Areas)."]}, {"cell_type": "markdown", "id": "7ff0d98c", "metadata": {}, "source": ["The `{Goal}::Message` construct proves `Goal`, possibly instantiating any variables in it, and sends `Message` to the resulting term.\n", "\n", "## Events and monitors\n", "\n", "Logtalk supports _event-driven programming_ by allowing defining events and monitors for those events. An event is simply the sending of a message to an object. Interpreting message sending as an atomic activity, a _before_ event and an _after_ event are recognized. Event monitors define event handler predicates, `before/3` and `after/3`, and can query, register, and delete a system-wide event registry that associates events with monitors. For example, a simple tracer for any message being sent using the `::/2` control construct can be defined as:"]}, {"cell_type": "code", "execution_count": 34, "id": "c491b2d4", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%file tracer.lgt\n", "\n", ":- object(tracer,\n", "\t% built-in protocol for event handlers\n", "\timplements(monitoring)).\n", "\n", "\t:- initialization(define_events(_, list, _, _, tracer)).\n", "\n", "\tbefore(Object, Message, Sender) :-\n", "\t\twrite('call: '), writeq(Object), write(' <-- '), writeq(Message),\n", "\t\twrite(' from '), writeq(Sender), nl.\n", "\n", "\tafter(Object, Message, Sender) :-\n", "\t\twrite('exit: '), writeq(Object), write(' <-- '), writeq(Message),\n", "\t\twrite(' from '), writeq(Sender), nl.\n", "\n", ":- end_object."]}, {"cell_type": "markdown", "id": "8dc09e6b", "metadata": {}, "source": ["Assuming that the `tracer` object and the `list` object defined earlier are compiled and loaded, we can observe the event handlers in action by sending a message:"]}, {"cell_type": "code", "execution_count": 35, "id": "23276d59", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["set_logtalk_flag(events, allow)."]}, {"cell_type": "code", "execution_count": 36, "id": "4eeaa7f6", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/markdown": ["X | \n", ":- | \n", "1 | \n", "2 | \n", "3 | "], "text/plain": ["X | \n", ":- | \n", "1 | \n", "2 | \n", "3 | "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["call: list <-- member(_R67861,[1,2,3]) from user\n", "exit: list <-- member(1,[1,2,3]) from user\n", "exit: list <-- member(2,[1,2,3]) from user\n", "exit: list <-- member(3,[1,2,3]) from user"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%table\n", "list::member(X, [1,2,3])."]}, {"cell_type": "markdown", "id": "385a8e48", "metadata": {}, "source": ["Events can be set and deleted dynamically at runtime by calling the `define_events/5` and `abolish_events/5` built-in predicates.\n", "\n", "Event-driven programming can be seen as a form of _computational reflection_. But note that events are only generated when using the `::/2` message-sending control construct."]}, {"cell_type": "code", "execution_count": 37, "id": "7617a38e", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["set_logtalk_flag(events, deny)."]}, {"cell_type": "markdown", "id": "9ed8be2d", "metadata": {}, "source": ["## Lambda expressions\n", "\n", "Logtalk supports lambda expressions. Lambda parameters are represented using a list with the `(>>)/2` infix operator connecting them to the lambda. Some simple examples using library `meta`:"]}, {"cell_type": "code", "execution_count": 38, "id": "d2d0818d", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["{meta(loader)}."]}, {"cell_type": "code", "execution_count": 39, "id": "c313eb4a", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mYs = [2,4,6]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["meta::map([X,Y]>>(Y is 2*X), [1,2,3], Ys)."]}, {"cell_type": "markdown", "id": "e2b033e4", "metadata": {}, "source": ["Currying is also supported:"]}, {"cell_type": "code", "execution_count": 40, "id": "76269f41", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mYs = [2,4,6]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["meta::map([X]>>([Y]>>(Y is 2*X)), [1,2,3], Ys)."]}, {"cell_type": "markdown", "id": "6f4a7d61", "metadata": {}, "source": ["Lambda free variables can be expressed using the extended syntax `{Free1, ...}/[Parameter1, ...]>>Lambda`.\n", "\n", "## <PERSON><PERSON>\n", "\n", "Terms and goals in source files can be _expanded_ at compile time by specifying a _hook object_ that defines term-expansion and goal-expansion rules.\n", "\n", "Assume the following hook object, saved in a `my_macros.lgt` file, that expands clauses and calls to the `foo/1` local predicate:"]}, {"cell_type": "code", "execution_count": 41, "id": "a2d08674", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%file my_macros.lgt\n", "\n", ":- object(my_macros,\n", "\t% built-in protocol for expanding predicates\n", "\timplements(expanding)).\n", "\n", "\tterm_expansion(foo(Char), baz(Code)) :-\n", "\t\t% standard built-in predicate\n", "\t\tchar_code(<PERSON><PERSON>, Code).\n", "\n", "\tgoal_expansion(foo(X), baz(X)).\n", "\n", ":- end_object."]}, {"cell_type": "markdown", "id": "1e13e832", "metadata": {}, "source": ["Consider the following simple object, saved in a `source.lgt` file:"]}, {"cell_type": "code", "execution_count": 42, "id": "1380fe7c", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%file source.lgt\n", "\n", ":- set_logtalk_flag(hook, my_macros).\n", "\n", ":- object(source).\n", "\n", "\t:- public(bar/1).\n", "\tbar(X) :- foo(X).\n", "\n", "\tfoo(a). foo(b). foo(c).\n", "\n", ":- end_object."]}, {"cell_type": "code", "execution_count": 43, "id": "96a9efe2", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/markdown": ["X | \n", ":- | \n", "97 | \n", "98 | \n", "99 | "], "text/plain": ["X | \n", ":- | \n", "97 | \n", "98 | \n", "99 | "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%table\n", "source::bar(X)."]}, {"cell_type": "markdown", "id": "91a389dc", "metadata": {}, "source": ["The Logtalk library provides support for combining hook objects using different workflows (for example, defining a pipeline of expansions).\n", "\n", "## Further information\n", "\n", "Visit the [Logtalk website](http://logtalk.org) for more information."]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk_kernel"}, "language_info": {"codemirror_mode": "logtalk", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "Logtalk"}}, "nbformat": 4, "nbformat_minor": 5}