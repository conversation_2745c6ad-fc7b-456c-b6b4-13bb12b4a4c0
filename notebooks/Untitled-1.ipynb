{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["eclipse."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mCodes = [65,66,67]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["atom_codes('ABC',Codes)."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mX = 'A'"]}, "metadata": {}, "output_type": "display_data"}], "source": ["X = 'A'."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mX = 'A',\n", "Y = 'A',\n", "Z = 'A'"]}, "metadata": {}, "output_type": "display_data"}], "source": ["X = Y, Y = Z, Z = 'A'."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mX = a('ABC'),\n", "Y = a('ABC')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["X = a('ABC'), X = Y."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/markdown": ["X | ABC | \n", ":- | :- | \n", "X | X | \n", "'DEF' | ABC | \n", "'GHI' | ABC | "], "text/plain": ["X | ABC | \n", ":- | :- | \n", "X | X | \n", "'DEF' | ABC | \n", "'GHI' | ABC | "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mYes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%table\n", "(X = ABC; X = 'DEF'; X = 'GHI')."]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "logtalk"}}, "source": ["jupyter_query_handling::query_data(Id, Runtime, TermData, OriginalTermData)."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["atom_codes('ABC',Codes),\n", "X='A',\n", "X=Y,Y=Z,Z='A',\n", "X=a('ABC'),X=Y."]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mYes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::print_queries."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["$Codes = [65,66,67]\n", "$X = a('ABC')\n", "$Y = a('ABC')\n", "$Z = 'A'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mYes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%bindings"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mX = a(Y)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["X = a(Y)."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["foo"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mYes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["write(user_error, foo)."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["atom_codes('ABC',Codes),\n", "X='A',\n", "X=Y,Y=Z,Z='A',\n", "X=a('ABC'),X=Y,\n", "jupyter::print_queries,\n", "jupyter::print_variable_bindings,\n", "X=a(Y),\n", "write(user_error,foo)."]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mYes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print_queries."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk_kernel"}, "language_info": {"codemirror_mode": "logtalk", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "logtalk"}}, "nbformat": 4, "nbformat_minor": 2}