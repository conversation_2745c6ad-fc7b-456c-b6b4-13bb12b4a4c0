Flag	Value
linter	default
always_true_or_false_goals	warning
arithmetic_expressions	warning
catchall_catch	silent
conditionals	warning
deprecated	warning
disjunctions	warning
duplicated_clauses	silent
duplicated_directives	warning
encodings	warning
general	warning
grammar_rules	warning
lambda_variables	warning
left_recursion	warning
missing_directives	warning
naming	silent
portability	silent
redefined_built_ins	silent
redefined_operators	warning
singleton_variables	warning
steadfastness	silent
suspicious_calls	warning
tail_recursive	silent
trivial_goal_fails	warning
undefined_predicates	warning
unknown_entities	warning
unknown_predicates	warning
complements	deny
context_switching_calls	allow
dynamic_declarations	deny
events	deny
clean	on
code_prefix	$
debug	off
optimize	off
reload	changed
report	warnings
scratch_directory	'./.lgt_tmp/'
source_data	on
version_data	logtalk(3,93,0,b01)
settings_file	allow
prolog_compatible_version	@>=(v(6,6,0))
prolog_dialect	swi
prolog_version	v(9,3,25)
underscore_variables	dont_care
coinduction	supported
encoding_directive	full
engines	supported
modules	supported
tabling	supported
threads	supported
unicode	full
prolog_compiler	[]
prolog_loader	[silent(true),optimise(true)]
suppress_path_prefix	''
