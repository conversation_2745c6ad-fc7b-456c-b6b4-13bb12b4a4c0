{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["%load_ext jupyter_ai"]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk_kernel"}, "language_info": {"codemirror_mode": "logtalk", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "Logtalk"}}, "nbformat": 4, "nbformat_minor": 2}