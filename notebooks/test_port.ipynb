{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7dc1f246", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/markdown": ["Port | \n", ":- | \n", "8901 | "], "text/plain": ["Port | \n", ":- | \n", "8901 | "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%table\n", "jupyter_widgets << webserver_port_(Port)."]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk_kernel"}, "language_info": {"codemirror_mode": "logtalk", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "Logtalk"}}, "nbformat": 4, "nbformat_minor": 5}