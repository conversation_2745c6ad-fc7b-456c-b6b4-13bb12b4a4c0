FROM deepnote/python:3.10

RUN apt update
RUN apt install -y xxd libffi-dev swi-prolog

RUN wget https://logtalk.org/files/logtalk_3.70.0-1_all.deb && dpkg -i logtalk_3.70.0-1_all.deb && rm logtalk_3.70.0-1_all.deb

ENV LOGTALKHOME "/usr/share/logtalk"
ENV LOGTALKUSER "/root/logtalk"

ENV INFOPATH=$INFOPATH:$LOGTALKHOME/docs:$LOGTALKHOME/manuals
ENV PATH=$PATH:$LOGTALKHOME/tools/diagrams
ENV PATH=$PATH:$LOGTALKHOME/tools/lgtdoc/xml
ENV PATH=$PATH:$LOGTALKHOME/scripts
ENV PATH=$PATH:$LOGTALKHOME/integration
ENV MANPATH=$MANPATH:$LOGTALKHOME/man

RUN python -m pip install --upgrade pip
RUN pip install jupyter-console
RUN pip install notebook
RUN pip install logtalk-jupyter-kernel==0.7.1 && python3 -m logtalk_kernel.install --user

# RUN git clone --depth 1 https://github.com/didoudiaz/gprolog && cd gprolog/src && ./configure --prefix=/in-place && make && make install && cd ~
# ENV PATH=$PATH:$HOME/gprolog/bin
# RUN gplgt --query-goal "halt"

RUN git clone --depth 1 https://github.com/trealla-prolog/trealla &&  cd trealla && make clean && make NOSSL=1 ISOCLINE=1 && cd ~
ENV PATH=$PATH:$HOME/trealla
RUN tplgt -g "halt"

RUN cd /root && jupyter console --kernel logtalk_kernel
ENV DEFAULT_KERNEL_NAME "logtalk_kernel"
