json([jsonrpc-2.0,id-0,result-true])
json([jsonrpc-2.0,id-0,result-json([1-json([status-success,type-query,bindings-json([]),output-])])])
json([jsonrpc-2.0,id-0,result-json([1-json([status-success,type-query,bindings-json([]),output-])])])
runtime_error(error(domain_error(json_source,line(<stream>(0x103366a50))),logtalk(parse(line(<stream>(0x103366a50)),_1618),c(json(list,dash,atom),json(list,dash,atom),r(jupyter_jsonrpc,json(list,dash,atom),[],[])))))
