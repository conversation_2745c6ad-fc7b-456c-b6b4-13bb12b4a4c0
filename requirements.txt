#
# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile pyproject.toml
#
appnope==0.1.3
    # via
    #   ipykernel
    #   ipython
asttokens==2.2.1
    # via stack-data
backcall==0.2.0
    # via ipython
beautifulsoup4==4.11.2
    # via logtalk-jupyter-kernel (pyproject.toml)
comm==0.1.2
    # via ipykernel
debugpy==1.6.6
    # via ipykernel
decorator==5.1.1
    # via ipython
executing==1.2.0
    # via stack-data
graphviz==0.20.1
    # via logtalk-jupyter-kernel (pyproject.toml)
ipykernel==6.21.2
    # via logtalk-jupyter-kernel (pyproject.toml)
ipython==8.10.0
    # via
    #   ipykernel
    #   logtalk-jupyter-kernel (pyproject.toml)
jedi==0.18.2
    # via ipython
jupyter-client==8.0.2
    # via
    #   ipykernel
    #   logtalk-jupyter-kernel (pyproject.toml)
jupyter-core==5.2.0
    # via
    #   ipykernel
    #   jupyter-client
matplotlib-inline==0.1.6
    # via
    #   ipykernel
    #   ipython
nest-asyncio==1.5.6
    # via ipykernel
packaging==23.0
    # via ipykernel
parso==0.8.3
    # via jedi
pexpect==4.8.0
    # via ipython
pickleshare==0.7.5
    # via ipython
platformdirs==3.0.0
    # via jupyter-core
prompt-toolkit==3.0.36
    # via ipython
psutil==5.9.4
    # via ipykernel
ptyprocess==0.7.0
    # via pexpect
pure-eval==0.2.2
    # via stack-data
pygments==2.14.0
    # via ipython
python-dateutil==2.8.2
    # via jupyter-client
pyzmq==25.0.0
    # via
    #   ipykernel
    #   jupyter-client
six==1.16.0
    # via python-dateutil
soupsieve==2.4
    # via beautifulsoup4
stack-data==0.6.2
    # via ipython
tornado==6.2
    # via
    #   ipykernel
    #   jupyter-client
traitlets==5.9.0
    # via
    #   comm
    #   ipykernel
    #   ipython
    #   jupyter-client
    #   jupyter-core
    #   matplotlib-inline
wcwidth==0.2.6
    # via prompt-toolkit
