{% set name = "logtalk-jupyter-kernel" %}
{% set version = "0.30.0" %}

package:
  name: {{ name|lower }}
  version: {{ version }}

source:
  url: https://pypi.io/packages/source/{{ name[0] }}/{{ name }}/logtalk-jupyter-kernel-{{ version }}.tar.gz
  sha256: 8664858ff17a53d10ffbb741b5722f53a256cd13e1e073f8b2ccadc255e53747

build:
  noarch: python
  script: {{ PYTHON }} -m pip install . -vv
  number: 0

requirements:
  host:
    - python >=3.7
    - setuptools >=61.0
    - pip
  run:
    - python >=3.7
    - jupyter_client
    - ipython
    - ipykernel
    - jupytext
    - python-graphviz
    - beautifulsoup4
    - matplotlib

test:
  imports:
    - logtalk_jupyter_kernel
  commands:
    - pip check
  requires:
    - pip

about:
  summary: Hercutalk - A Jupyter Kernel for Logtalk
  dev_url: https://github.com/LogtalkDotOrg/logtalk-jupyter-kernel
  license: MIT
  license_file: LICENSE

extra:
  recipe-maintainers:
    - pmoura
